{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Graduation\\\\Frontend\\\\admin\\\\src\\\\components\\\\Dashboard.jsx\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container mx-auto px-4 py-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"text-3xl font-bold text-gray-800 mb-8\",\n      children: \"T\\u1ED5ng quan h\\u1EC7 th\\u1ED1ng\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-md p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold mb-4\",\n          children: \"Crawl Data\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-4\",\n          children: \"Crawl d\\u1EEF li\\u1EC7u t\\u1EEB c\\xE1c ngu\\u1ED3n b\\xEAn ngo\\xE0i\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/crawl\",\n          className: \"bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700 inline-block\",\n          children: \"\\u0110i \\u0111\\u1EBFn\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-md p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold mb-4\",\n          children: \"Update Data\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-4\",\n          children: \"C\\u1EADp nh\\u1EADt v\\xE0 x\\u1EED l\\xFD d\\u1EEF li\\u1EC7u \\u0111\\xE3 crawl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/update\",\n          className: \"bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700 inline-block\",\n          children: \"\\u0110i \\u0111\\u1EBFn\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-md p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold mb-4\",\n          children: \"Danh s\\xE1ch b\\xE0i vi\\u1EBFt\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-4\",\n          children: \"Xem t\\u1EA5t c\\u1EA3 c\\xE1c b\\xE0i vi\\u1EBFt \\u0111\\xE3 crawl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/posts\",\n          className: \"bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700 inline-block\",\n          children: \"\\u0110i \\u0111\\u1EBFn\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "Link", "jsxDEV", "_jsxDEV", "Dashboard", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Graduation/Frontend/admin/src/components/Dashboard.jsx"], "sourcesContent": ["import React from 'react';\r\nimport { Link } from 'react-router-dom';\r\n\r\nconst Dashboard = () => {\r\n  return (\r\n    <div className=\"container mx-auto px-4 py-6\">\r\n      <h1 className=\"text-3xl font-bold text-gray-800 mb-8\">Tổ<PERSON> quan hệ thống</h1>\r\n      \r\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\r\n        <div className=\"bg-white rounded-lg shadow-md p-6\">\r\n          <h2 className=\"text-xl font-semibold mb-4\">Crawl Data</h2>\r\n          <p className=\"text-gray-600 mb-4\">Crawl dữ liệu từ các nguồn bên ngoài</p>\r\n          <Link to=\"/crawl\" className=\"bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700 inline-block\">\r\n            Đi đến\r\n          </Link>\r\n        </div>\r\n        \r\n        <div className=\"bg-white rounded-lg shadow-md p-6\">\r\n          <h2 className=\"text-xl font-semibold mb-4\">Update Data</h2>\r\n          <p className=\"text-gray-600 mb-4\">Cậ<PERSON> nh<PERSON>t và xử lý dữ liệu đã crawl</p>\r\n          <Link to=\"/update\" className=\"bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700 inline-block\">\r\n            Đi đến\r\n          </Link>\r\n        </div>\r\n        \r\n        <div className=\"bg-white rounded-lg shadow-md p-6\">\r\n          <h2 className=\"text-xl font-semibold mb-4\">Danh sách bài viết</h2>\r\n          <p className=\"text-gray-600 mb-4\">Xem tất cả các bài viết đã crawl</p>\r\n          <Link to=\"/posts\" className=\"bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700 inline-block\">\r\n            Đi đến\r\n          </Link>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Dashboard;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,SAAS,GAAGA,CAAA,KAAM;EACtB,oBACED,OAAA;IAAKE,SAAS,EAAC,6BAA6B;IAAAC,QAAA,gBAC1CH,OAAA;MAAIE,SAAS,EAAC,uCAAuC;MAAAC,QAAA,EAAC;IAAkB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAE7EP,OAAA;MAAKE,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBACpDH,OAAA;QAAKE,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDH,OAAA;UAAIE,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1DP,OAAA;UAAGE,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAoC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC1EP,OAAA,CAACF,IAAI;UAACU,EAAE,EAAC,QAAQ;UAACN,SAAS,EAAC,6EAA6E;UAAAC,QAAA,EAAC;QAE1G;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENP,OAAA;QAAKE,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDH,OAAA;UAAIE,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3DP,OAAA;UAAGE,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAkC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACxEP,OAAA,CAACF,IAAI;UAACU,EAAE,EAAC,SAAS;UAACN,SAAS,EAAC,6EAA6E;UAAAC,QAAA,EAAC;QAE3G;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENP,OAAA;QAAKE,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDH,OAAA;UAAIE,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClEP,OAAA;UAAGE,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAgC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACtEP,OAAA,CAACF,IAAI;UAACU,EAAE,EAAC,QAAQ;UAACN,SAAS,EAAC,6EAA6E;UAAAC,QAAA,EAAC;QAE1G;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACE,EAAA,GAhCIR,SAAS;AAkCf,eAAeA,SAAS;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}