{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Graduation\\\\Frontend\\\\admin\\\\src\\\\components\\\\ProgressBar.jsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProgressBar = ({\n  progress\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-full bg-gray-200 rounded-full h-4 mb-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-indigo-600 h-4 rounded-full transition-all duration-500 ease-in-out\",\n      style: {\n        width: `${progress}%`\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-right mt-1 text-sm text-gray-600\",\n      children: [progress, \"% Ho\\xE0n th\\xE0nh\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = ProgressBar;\nexport default ProgressBar;\nvar _c;\n$RefreshReg$(_c, \"ProgressBar\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "ProgressBar", "progress", "className", "children", "style", "width", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Graduation/Frontend/admin/src/components/ProgressBar.jsx"], "sourcesContent": ["import React from 'react';\r\n\r\nconst ProgressBar = ({ progress }) => {\r\n  return (\r\n    <div className=\"w-full bg-gray-200 rounded-full h-4 mb-4\">\r\n      <div \r\n        className=\"bg-indigo-600 h-4 rounded-full transition-all duration-500 ease-in-out\"\r\n        style={{ width: `${progress}%` }}\r\n      >\r\n      </div>\r\n      <div className=\"text-right mt-1 text-sm text-gray-600\">\r\n        {progress}% Hoàn thành\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProgressBar;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,WAAW,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EACpC,oBACEF,OAAA;IAAKG,SAAS,EAAC,0CAA0C;IAAAC,QAAA,gBACvDJ,OAAA;MACEG,SAAS,EAAC,wEAAwE;MAClFE,KAAK,EAAE;QAAEC,KAAK,EAAE,GAAGJ,QAAQ;MAAI;IAAE;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAE9B,CAAC,eACNV,OAAA;MAAKG,SAAS,EAAC,uCAAuC;MAAAC,QAAA,GACnDF,QAAQ,EAAC,oBACZ;IAAA;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACC,EAAA,GAbIV,WAAW;AAejB,eAAeA,WAAW;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}