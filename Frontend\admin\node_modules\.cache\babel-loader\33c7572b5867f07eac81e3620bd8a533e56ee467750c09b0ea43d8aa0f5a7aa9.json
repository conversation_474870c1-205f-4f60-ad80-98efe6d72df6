{"ast": null, "code": "/**\n * react-router v7.2.0\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport { FrameworkContext, RemixErrorBoundary, RouterProvider, createBrowserHistory, createClientRoutes, createClientRoutesWithHMRRevalidationOptOut, createRouter, decodeViaTurboStream, deserializeErrors, getPatchRoutesOnNavigationFunction, getSingleFetchDataStrategy, invariant, mapRouteProperties, matchRoutes, shouldHydrateRouteLoader, useFogOFWarDiscovery } from \"./chunk-HA7DTUK3.mjs\";\n\n// lib/dom-export/dom-router-provider.tsx\nimport * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nfunction RouterProvider2(props) {\n  return /* @__PURE__ */React.createElement(RouterProvider, {\n    flushSync: ReactDOM.flushSync,\n    ...props\n  });\n}\n\n// lib/dom-export/hydrated-router.tsx\nimport * as React2 from \"react\";\nvar ssrInfo = null;\nvar router = null;\nfunction initSsrInfo() {\n  if (!ssrInfo && window.__reactRouterContext && window.__reactRouterManifest && window.__reactRouterRouteModules) {\n    ssrInfo = {\n      context: window.__reactRouterContext,\n      manifest: window.__reactRouterManifest,\n      routeModules: window.__reactRouterRouteModules,\n      stateDecodingPromise: void 0,\n      router: void 0,\n      routerInitialized: false\n    };\n  }\n}\nfunction createHydratedRouter() {\n  initSsrInfo();\n  if (!ssrInfo) {\n    throw new Error(\"You must be using the SSR features of React Router in order to skip passing a `router` prop to `<RouterProvider>`\");\n  }\n  let localSsrInfo = ssrInfo;\n  if (!ssrInfo.stateDecodingPromise) {\n    let stream = ssrInfo.context.stream;\n    invariant(stream, \"No stream found for single fetch decoding\");\n    ssrInfo.context.stream = void 0;\n    ssrInfo.stateDecodingPromise = decodeViaTurboStream(stream, window).then(value => {\n      ssrInfo.context.state = value.value;\n      localSsrInfo.stateDecodingPromise.value = true;\n    }).catch(e => {\n      localSsrInfo.stateDecodingPromise.error = e;\n    });\n  }\n  if (ssrInfo.stateDecodingPromise.error) {\n    throw ssrInfo.stateDecodingPromise.error;\n  }\n  if (!ssrInfo.stateDecodingPromise.value) {\n    throw ssrInfo.stateDecodingPromise;\n  }\n  let routes = createClientRoutes(ssrInfo.manifest.routes, ssrInfo.routeModules, ssrInfo.context.state, ssrInfo.context.ssr, ssrInfo.context.isSpaMode);\n  let hydrationData = void 0;\n  let loaderData = ssrInfo.context.state.loaderData;\n  if (ssrInfo.context.isSpaMode) {\n    hydrationData = {\n      loaderData\n    };\n  } else {\n    hydrationData = {\n      ...ssrInfo.context.state,\n      loaderData: {\n        ...loaderData\n      }\n    };\n    let initialMatches = matchRoutes(routes, window.location, window.__reactRouterContext?.basename);\n    if (initialMatches) {\n      for (let match of initialMatches) {\n        let routeId = match.route.id;\n        let route = ssrInfo.routeModules[routeId];\n        let manifestRoute = ssrInfo.manifest.routes[routeId];\n        if (route && manifestRoute && shouldHydrateRouteLoader(manifestRoute, route, ssrInfo.context.isSpaMode) && (route.HydrateFallback || !manifestRoute.hasLoader)) {\n          delete hydrationData.loaderData[routeId];\n        } else if (manifestRoute && !manifestRoute.hasLoader) {\n          hydrationData.loaderData[routeId] = null;\n        }\n      }\n    }\n    if (hydrationData && hydrationData.errors) {\n      hydrationData.errors = deserializeErrors(hydrationData.errors);\n    }\n  }\n  let router2 = createRouter({\n    routes,\n    history: createBrowserHistory(),\n    basename: ssrInfo.context.basename,\n    hydrationData,\n    mapRouteProperties,\n    dataStrategy: getSingleFetchDataStrategy(ssrInfo.manifest, ssrInfo.routeModules, ssrInfo.context.ssr, () => router2),\n    patchRoutesOnNavigation: getPatchRoutesOnNavigationFunction(ssrInfo.manifest, ssrInfo.routeModules, ssrInfo.context.ssr, ssrInfo.context.isSpaMode, ssrInfo.context.basename)\n  });\n  ssrInfo.router = router2;\n  if (router2.state.initialized) {\n    ssrInfo.routerInitialized = true;\n    router2.initialize();\n  }\n  router2.createRoutesForHMR = /* spacer so ts-ignore does not affect the right hand of the assignment */\n  createClientRoutesWithHMRRevalidationOptOut;\n  window.__reactRouterDataRouter = router2;\n  return router2;\n}\nfunction HydratedRouter() {\n  if (!router) {\n    router = createHydratedRouter();\n  }\n  let [criticalCss, setCriticalCss] = React2.useState(process.env.NODE_ENV === \"development\" ? ssrInfo?.context.criticalCss : void 0);\n  if (process.env.NODE_ENV === \"development\") {\n    if (ssrInfo) {\n      window.__reactRouterClearCriticalCss = () => setCriticalCss(void 0);\n    }\n  }\n  let [location, setLocation] = React2.useState(router.state.location);\n  React2.useLayoutEffect(() => {\n    if (ssrInfo && ssrInfo.router && !ssrInfo.routerInitialized) {\n      ssrInfo.routerInitialized = true;\n      ssrInfo.router.initialize();\n    }\n  }, []);\n  React2.useLayoutEffect(() => {\n    if (ssrInfo && ssrInfo.router) {\n      return ssrInfo.router.subscribe(newState => {\n        if (newState.location !== location) {\n          setLocation(newState.location);\n        }\n      });\n    }\n  }, [location]);\n  invariant(ssrInfo, \"ssrInfo unavailable for HydratedRouter\");\n  useFogOFWarDiscovery(router, ssrInfo.manifest, ssrInfo.routeModules, ssrInfo.context.ssr, ssrInfo.context.isSpaMode);\n  return (\n    // This fragment is important to ensure we match the <ServerRouter> JSX\n    // structure so that useId values hydrate correctly\n    /* @__PURE__ */\n    React2.createElement(React2.Fragment, null, /* @__PURE__ */React2.createElement(FrameworkContext.Provider, {\n      value: {\n        manifest: ssrInfo.manifest,\n        routeModules: ssrInfo.routeModules,\n        future: ssrInfo.context.future,\n        criticalCss,\n        ssr: ssrInfo.context.ssr,\n        isSpaMode: ssrInfo.context.isSpaMode\n      }\n    }, /* @__PURE__ */React2.createElement(RemixErrorBoundary, {\n      location\n    }, /* @__PURE__ */React2.createElement(RouterProvider2, {\n      router\n    }))), /* @__PURE__ */React2.createElement(React2.Fragment, null))\n  );\n}\nexport { HydratedRouter, RouterProvider2 as RouterProvider };", "map": {"version": 3, "names": ["FrameworkContext", "RemixErrorBoundary", "RouterProvider", "createBrowserHistory", "createClientRoutes", "createClientRoutesWithHMRRevalidationOptOut", "createRouter", "decodeViaTurboStream", "deserializeErrors", "getPatchRoutesOnNavigationFunction", "getSingleFetchDataStrategy", "invariant", "mapRouteProperties", "matchRoutes", "shouldHydrateRouteLoader", "useFogOFWarDiscovery", "React", "ReactDOM", "RouterProvider2", "props", "createElement", "flushSync", "React2", "ssrInfo", "router", "initSsrInfo", "window", "__reactRouterContext", "__reactRouterManifest", "__reactRouterRouteModules", "context", "manifest", "routeModules", "stateDecodingPromise", "routerInitialized", "createHydratedRouter", "Error", "localSsrInfo", "stream", "then", "value", "state", "catch", "e", "error", "routes", "ssr", "isSpaMode", "hydrationData", "loaderData", "initialMatches", "location", "basename", "match", "routeId", "route", "id", "manifestRoute", "HydrateFallback", "<PERSON><PERSON><PERSON><PERSON>", "errors", "router2", "history", "dataStrategy", "patchRoutesOnNavigation", "initialized", "initialize", "createRoutesForHMR", "__reactRouterDataRouter", "HydratedRouter", "criticalCss", "setCriticalCss", "useState", "process", "env", "NODE_ENV", "__reactRouterClearCriticalCss", "setLocation", "useLayoutEffect", "subscribe", "newState", "Fragment", "Provider", "future"], "sources": ["C:/Users/<USER>/Graduation/Frontend/admin/node_modules/react-router/dist/development/dom-export.mjs"], "sourcesContent": ["/**\n * react-router v7.2.0\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport {\n  FrameworkContext,\n  RemixErrorBoundary,\n  RouterProvider,\n  createBrowserHistory,\n  createClientRoutes,\n  createClientRoutesWithHMRRevalidationOptOut,\n  createRouter,\n  decodeViaTurboStream,\n  deserializeErrors,\n  getPatchRoutesOnNavigationFunction,\n  getSingleFetchDataStrategy,\n  invariant,\n  mapRouteProperties,\n  matchRoutes,\n  shouldHydrateRouteLoader,\n  useFogOFWarDiscovery\n} from \"./chunk-HA7DTUK3.mjs\";\n\n// lib/dom-export/dom-router-provider.tsx\nimport * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nfunction RouterProvider2(props) {\n  return /* @__PURE__ */ React.createElement(RouterProvider, { flushSync: ReactDOM.flushSync, ...props });\n}\n\n// lib/dom-export/hydrated-router.tsx\nimport * as React2 from \"react\";\nvar ssrInfo = null;\nvar router = null;\nfunction initSsrInfo() {\n  if (!ssrInfo && window.__reactRouterContext && window.__reactRouterManifest && window.__reactRouterRouteModules) {\n    ssrInfo = {\n      context: window.__reactRouterContext,\n      manifest: window.__reactRouterManifest,\n      routeModules: window.__reactRouterRouteModules,\n      stateDecodingPromise: void 0,\n      router: void 0,\n      routerInitialized: false\n    };\n  }\n}\nfunction createHydratedRouter() {\n  initSsrInfo();\n  if (!ssrInfo) {\n    throw new Error(\n      \"You must be using the SSR features of React Router in order to skip passing a `router` prop to `<RouterProvider>`\"\n    );\n  }\n  let localSsrInfo = ssrInfo;\n  if (!ssrInfo.stateDecodingPromise) {\n    let stream = ssrInfo.context.stream;\n    invariant(stream, \"No stream found for single fetch decoding\");\n    ssrInfo.context.stream = void 0;\n    ssrInfo.stateDecodingPromise = decodeViaTurboStream(stream, window).then((value) => {\n      ssrInfo.context.state = value.value;\n      localSsrInfo.stateDecodingPromise.value = true;\n    }).catch((e) => {\n      localSsrInfo.stateDecodingPromise.error = e;\n    });\n  }\n  if (ssrInfo.stateDecodingPromise.error) {\n    throw ssrInfo.stateDecodingPromise.error;\n  }\n  if (!ssrInfo.stateDecodingPromise.value) {\n    throw ssrInfo.stateDecodingPromise;\n  }\n  let routes = createClientRoutes(\n    ssrInfo.manifest.routes,\n    ssrInfo.routeModules,\n    ssrInfo.context.state,\n    ssrInfo.context.ssr,\n    ssrInfo.context.isSpaMode\n  );\n  let hydrationData = void 0;\n  let loaderData = ssrInfo.context.state.loaderData;\n  if (ssrInfo.context.isSpaMode) {\n    hydrationData = { loaderData };\n  } else {\n    hydrationData = {\n      ...ssrInfo.context.state,\n      loaderData: { ...loaderData }\n    };\n    let initialMatches = matchRoutes(\n      routes,\n      window.location,\n      window.__reactRouterContext?.basename\n    );\n    if (initialMatches) {\n      for (let match of initialMatches) {\n        let routeId = match.route.id;\n        let route = ssrInfo.routeModules[routeId];\n        let manifestRoute = ssrInfo.manifest.routes[routeId];\n        if (route && manifestRoute && shouldHydrateRouteLoader(\n          manifestRoute,\n          route,\n          ssrInfo.context.isSpaMode\n        ) && (route.HydrateFallback || !manifestRoute.hasLoader)) {\n          delete hydrationData.loaderData[routeId];\n        } else if (manifestRoute && !manifestRoute.hasLoader) {\n          hydrationData.loaderData[routeId] = null;\n        }\n      }\n    }\n    if (hydrationData && hydrationData.errors) {\n      hydrationData.errors = deserializeErrors(hydrationData.errors);\n    }\n  }\n  let router2 = createRouter({\n    routes,\n    history: createBrowserHistory(),\n    basename: ssrInfo.context.basename,\n    hydrationData,\n    mapRouteProperties,\n    dataStrategy: getSingleFetchDataStrategy(\n      ssrInfo.manifest,\n      ssrInfo.routeModules,\n      ssrInfo.context.ssr,\n      () => router2\n    ),\n    patchRoutesOnNavigation: getPatchRoutesOnNavigationFunction(\n      ssrInfo.manifest,\n      ssrInfo.routeModules,\n      ssrInfo.context.ssr,\n      ssrInfo.context.isSpaMode,\n      ssrInfo.context.basename\n    )\n  });\n  ssrInfo.router = router2;\n  if (router2.state.initialized) {\n    ssrInfo.routerInitialized = true;\n    router2.initialize();\n  }\n  router2.createRoutesForHMR = /* spacer so ts-ignore does not affect the right hand of the assignment */\n  createClientRoutesWithHMRRevalidationOptOut;\n  window.__reactRouterDataRouter = router2;\n  return router2;\n}\nfunction HydratedRouter() {\n  if (!router) {\n    router = createHydratedRouter();\n  }\n  let [criticalCss, setCriticalCss] = React2.useState(\n    process.env.NODE_ENV === \"development\" ? ssrInfo?.context.criticalCss : void 0\n  );\n  if (process.env.NODE_ENV === \"development\") {\n    if (ssrInfo) {\n      window.__reactRouterClearCriticalCss = () => setCriticalCss(void 0);\n    }\n  }\n  let [location, setLocation] = React2.useState(router.state.location);\n  React2.useLayoutEffect(() => {\n    if (ssrInfo && ssrInfo.router && !ssrInfo.routerInitialized) {\n      ssrInfo.routerInitialized = true;\n      ssrInfo.router.initialize();\n    }\n  }, []);\n  React2.useLayoutEffect(() => {\n    if (ssrInfo && ssrInfo.router) {\n      return ssrInfo.router.subscribe((newState) => {\n        if (newState.location !== location) {\n          setLocation(newState.location);\n        }\n      });\n    }\n  }, [location]);\n  invariant(ssrInfo, \"ssrInfo unavailable for HydratedRouter\");\n  useFogOFWarDiscovery(\n    router,\n    ssrInfo.manifest,\n    ssrInfo.routeModules,\n    ssrInfo.context.ssr,\n    ssrInfo.context.isSpaMode\n  );\n  return (\n    // This fragment is important to ensure we match the <ServerRouter> JSX\n    // structure so that useId values hydrate correctly\n    /* @__PURE__ */ React2.createElement(React2.Fragment, null, /* @__PURE__ */ React2.createElement(\n      FrameworkContext.Provider,\n      {\n        value: {\n          manifest: ssrInfo.manifest,\n          routeModules: ssrInfo.routeModules,\n          future: ssrInfo.context.future,\n          criticalCss,\n          ssr: ssrInfo.context.ssr,\n          isSpaMode: ssrInfo.context.isSpaMode\n        }\n      },\n      /* @__PURE__ */ React2.createElement(RemixErrorBoundary, { location }, /* @__PURE__ */ React2.createElement(RouterProvider2, { router }))\n    ), /* @__PURE__ */ React2.createElement(React2.Fragment, null))\n  );\n}\nexport {\n  HydratedRouter,\n  RouterProvider2 as RouterProvider\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SACEA,gBAAgB,EAChBC,kBAAkB,EAClBC,cAAc,EACdC,oBAAoB,EACpBC,kBAAkB,EAClBC,2CAA2C,EAC3CC,YAAY,EACZC,oBAAoB,EACpBC,iBAAiB,EACjBC,kCAAkC,EAClCC,0BAA0B,EAC1BC,SAAS,EACTC,kBAAkB,EAClBC,WAAW,EACXC,wBAAwB,EACxBC,oBAAoB,QACf,sBAAsB;;AAE7B;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,QAAQ,MAAM,WAAW;AACrC,SAASC,eAAeA,CAACC,KAAK,EAAE;EAC9B,OAAO,eAAgBH,KAAK,CAACI,aAAa,CAAClB,cAAc,EAAE;IAAEmB,SAAS,EAAEJ,QAAQ,CAACI,SAAS;IAAE,GAAGF;EAAM,CAAC,CAAC;AACzG;;AAEA;AACA,OAAO,KAAKG,MAAM,MAAM,OAAO;AAC/B,IAAIC,OAAO,GAAG,IAAI;AAClB,IAAIC,MAAM,GAAG,IAAI;AACjB,SAASC,WAAWA,CAAA,EAAG;EACrB,IAAI,CAACF,OAAO,IAAIG,MAAM,CAACC,oBAAoB,IAAID,MAAM,CAACE,qBAAqB,IAAIF,MAAM,CAACG,yBAAyB,EAAE;IAC/GN,OAAO,GAAG;MACRO,OAAO,EAAEJ,MAAM,CAACC,oBAAoB;MACpCI,QAAQ,EAAEL,MAAM,CAACE,qBAAqB;MACtCI,YAAY,EAAEN,MAAM,CAACG,yBAAyB;MAC9CI,oBAAoB,EAAE,KAAK,CAAC;MAC5BT,MAAM,EAAE,KAAK,CAAC;MACdU,iBAAiB,EAAE;IACrB,CAAC;EACH;AACF;AACA,SAASC,oBAAoBA,CAAA,EAAG;EAC9BV,WAAW,CAAC,CAAC;EACb,IAAI,CAACF,OAAO,EAAE;IACZ,MAAM,IAAIa,KAAK,CACb,mHACF,CAAC;EACH;EACA,IAAIC,YAAY,GAAGd,OAAO;EAC1B,IAAI,CAACA,OAAO,CAACU,oBAAoB,EAAE;IACjC,IAAIK,MAAM,GAAGf,OAAO,CAACO,OAAO,CAACQ,MAAM;IACnC3B,SAAS,CAAC2B,MAAM,EAAE,2CAA2C,CAAC;IAC9Df,OAAO,CAACO,OAAO,CAACQ,MAAM,GAAG,KAAK,CAAC;IAC/Bf,OAAO,CAACU,oBAAoB,GAAG1B,oBAAoB,CAAC+B,MAAM,EAAEZ,MAAM,CAAC,CAACa,IAAI,CAAEC,KAAK,IAAK;MAClFjB,OAAO,CAACO,OAAO,CAACW,KAAK,GAAGD,KAAK,CAACA,KAAK;MACnCH,YAAY,CAACJ,oBAAoB,CAACO,KAAK,GAAG,IAAI;IAChD,CAAC,CAAC,CAACE,KAAK,CAAEC,CAAC,IAAK;MACdN,YAAY,CAACJ,oBAAoB,CAACW,KAAK,GAAGD,CAAC;IAC7C,CAAC,CAAC;EACJ;EACA,IAAIpB,OAAO,CAACU,oBAAoB,CAACW,KAAK,EAAE;IACtC,MAAMrB,OAAO,CAACU,oBAAoB,CAACW,KAAK;EAC1C;EACA,IAAI,CAACrB,OAAO,CAACU,oBAAoB,CAACO,KAAK,EAAE;IACvC,MAAMjB,OAAO,CAACU,oBAAoB;EACpC;EACA,IAAIY,MAAM,GAAGzC,kBAAkB,CAC7BmB,OAAO,CAACQ,QAAQ,CAACc,MAAM,EACvBtB,OAAO,CAACS,YAAY,EACpBT,OAAO,CAACO,OAAO,CAACW,KAAK,EACrBlB,OAAO,CAACO,OAAO,CAACgB,GAAG,EACnBvB,OAAO,CAACO,OAAO,CAACiB,SAClB,CAAC;EACD,IAAIC,aAAa,GAAG,KAAK,CAAC;EAC1B,IAAIC,UAAU,GAAG1B,OAAO,CAACO,OAAO,CAACW,KAAK,CAACQ,UAAU;EACjD,IAAI1B,OAAO,CAACO,OAAO,CAACiB,SAAS,EAAE;IAC7BC,aAAa,GAAG;MAAEC;IAAW,CAAC;EAChC,CAAC,MAAM;IACLD,aAAa,GAAG;MACd,GAAGzB,OAAO,CAACO,OAAO,CAACW,KAAK;MACxBQ,UAAU,EAAE;QAAE,GAAGA;MAAW;IAC9B,CAAC;IACD,IAAIC,cAAc,GAAGrC,WAAW,CAC9BgC,MAAM,EACNnB,MAAM,CAACyB,QAAQ,EACfzB,MAAM,CAACC,oBAAoB,EAAEyB,QAC/B,CAAC;IACD,IAAIF,cAAc,EAAE;MAClB,KAAK,IAAIG,KAAK,IAAIH,cAAc,EAAE;QAChC,IAAII,OAAO,GAAGD,KAAK,CAACE,KAAK,CAACC,EAAE;QAC5B,IAAID,KAAK,GAAGhC,OAAO,CAACS,YAAY,CAACsB,OAAO,CAAC;QACzC,IAAIG,aAAa,GAAGlC,OAAO,CAACQ,QAAQ,CAACc,MAAM,CAACS,OAAO,CAAC;QACpD,IAAIC,KAAK,IAAIE,aAAa,IAAI3C,wBAAwB,CACpD2C,aAAa,EACbF,KAAK,EACLhC,OAAO,CAACO,OAAO,CAACiB,SAClB,CAAC,KAAKQ,KAAK,CAACG,eAAe,IAAI,CAACD,aAAa,CAACE,SAAS,CAAC,EAAE;UACxD,OAAOX,aAAa,CAACC,UAAU,CAACK,OAAO,CAAC;QAC1C,CAAC,MAAM,IAAIG,aAAa,IAAI,CAACA,aAAa,CAACE,SAAS,EAAE;UACpDX,aAAa,CAACC,UAAU,CAACK,OAAO,CAAC,GAAG,IAAI;QAC1C;MACF;IACF;IACA,IAAIN,aAAa,IAAIA,aAAa,CAACY,MAAM,EAAE;MACzCZ,aAAa,CAACY,MAAM,GAAGpD,iBAAiB,CAACwC,aAAa,CAACY,MAAM,CAAC;IAChE;EACF;EACA,IAAIC,OAAO,GAAGvD,YAAY,CAAC;IACzBuC,MAAM;IACNiB,OAAO,EAAE3D,oBAAoB,CAAC,CAAC;IAC/BiD,QAAQ,EAAE7B,OAAO,CAACO,OAAO,CAACsB,QAAQ;IAClCJ,aAAa;IACbpC,kBAAkB;IAClBmD,YAAY,EAAErD,0BAA0B,CACtCa,OAAO,CAACQ,QAAQ,EAChBR,OAAO,CAACS,YAAY,EACpBT,OAAO,CAACO,OAAO,CAACgB,GAAG,EACnB,MAAMe,OACR,CAAC;IACDG,uBAAuB,EAAEvD,kCAAkC,CACzDc,OAAO,CAACQ,QAAQ,EAChBR,OAAO,CAACS,YAAY,EACpBT,OAAO,CAACO,OAAO,CAACgB,GAAG,EACnBvB,OAAO,CAACO,OAAO,CAACiB,SAAS,EACzBxB,OAAO,CAACO,OAAO,CAACsB,QAClB;EACF,CAAC,CAAC;EACF7B,OAAO,CAACC,MAAM,GAAGqC,OAAO;EACxB,IAAIA,OAAO,CAACpB,KAAK,CAACwB,WAAW,EAAE;IAC7B1C,OAAO,CAACW,iBAAiB,GAAG,IAAI;IAChC2B,OAAO,CAACK,UAAU,CAAC,CAAC;EACtB;EACAL,OAAO,CAACM,kBAAkB,GAAG;EAC7B9D,2CAA2C;EAC3CqB,MAAM,CAAC0C,uBAAuB,GAAGP,OAAO;EACxC,OAAOA,OAAO;AAChB;AACA,SAASQ,cAAcA,CAAA,EAAG;EACxB,IAAI,CAAC7C,MAAM,EAAE;IACXA,MAAM,GAAGW,oBAAoB,CAAC,CAAC;EACjC;EACA,IAAI,CAACmC,WAAW,EAAEC,cAAc,CAAC,GAAGjD,MAAM,CAACkD,QAAQ,CACjDC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,GAAGpD,OAAO,EAAEO,OAAO,CAACwC,WAAW,GAAG,KAAK,CAC/E,CAAC;EACD,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;IAC1C,IAAIpD,OAAO,EAAE;MACXG,MAAM,CAACkD,6BAA6B,GAAG,MAAML,cAAc,CAAC,KAAK,CAAC,CAAC;IACrE;EACF;EACA,IAAI,CAACpB,QAAQ,EAAE0B,WAAW,CAAC,GAAGvD,MAAM,CAACkD,QAAQ,CAAChD,MAAM,CAACiB,KAAK,CAACU,QAAQ,CAAC;EACpE7B,MAAM,CAACwD,eAAe,CAAC,MAAM;IAC3B,IAAIvD,OAAO,IAAIA,OAAO,CAACC,MAAM,IAAI,CAACD,OAAO,CAACW,iBAAiB,EAAE;MAC3DX,OAAO,CAACW,iBAAiB,GAAG,IAAI;MAChCX,OAAO,CAACC,MAAM,CAAC0C,UAAU,CAAC,CAAC;IAC7B;EACF,CAAC,EAAE,EAAE,CAAC;EACN5C,MAAM,CAACwD,eAAe,CAAC,MAAM;IAC3B,IAAIvD,OAAO,IAAIA,OAAO,CAACC,MAAM,EAAE;MAC7B,OAAOD,OAAO,CAACC,MAAM,CAACuD,SAAS,CAAEC,QAAQ,IAAK;QAC5C,IAAIA,QAAQ,CAAC7B,QAAQ,KAAKA,QAAQ,EAAE;UAClC0B,WAAW,CAACG,QAAQ,CAAC7B,QAAQ,CAAC;QAChC;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EACdxC,SAAS,CAACY,OAAO,EAAE,wCAAwC,CAAC;EAC5DR,oBAAoB,CAClBS,MAAM,EACND,OAAO,CAACQ,QAAQ,EAChBR,OAAO,CAACS,YAAY,EACpBT,OAAO,CAACO,OAAO,CAACgB,GAAG,EACnBvB,OAAO,CAACO,OAAO,CAACiB,SAClB,CAAC;EACD;IACE;IACA;IACA;IAAgBzB,MAAM,CAACF,aAAa,CAACE,MAAM,CAAC2D,QAAQ,EAAE,IAAI,EAAE,eAAgB3D,MAAM,CAACF,aAAa,CAC9FpB,gBAAgB,CAACkF,QAAQ,EACzB;MACE1C,KAAK,EAAE;QACLT,QAAQ,EAAER,OAAO,CAACQ,QAAQ;QAC1BC,YAAY,EAAET,OAAO,CAACS,YAAY;QAClCmD,MAAM,EAAE5D,OAAO,CAACO,OAAO,CAACqD,MAAM;QAC9Bb,WAAW;QACXxB,GAAG,EAAEvB,OAAO,CAACO,OAAO,CAACgB,GAAG;QACxBC,SAAS,EAAExB,OAAO,CAACO,OAAO,CAACiB;MAC7B;IACF,CAAC,EACD,eAAgBzB,MAAM,CAACF,aAAa,CAACnB,kBAAkB,EAAE;MAAEkD;IAAS,CAAC,EAAE,eAAgB7B,MAAM,CAACF,aAAa,CAACF,eAAe,EAAE;MAAEM;IAAO,CAAC,CAAC,CAC1I,CAAC,EAAE,eAAgBF,MAAM,CAACF,aAAa,CAACE,MAAM,CAAC2D,QAAQ,EAAE,IAAI,CAAC;EAAC;AAEnE;AACA,SACEZ,cAAc,EACdnD,eAAe,IAAIhB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}