{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Graduation\\\\Frontend\\\\admin\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport Navbar from './components/Navbar';\nimport Sidebar from './components/Sidebar';\nimport Dashboard from './components/Dashboard';\nimport DataCrawler from './components/DataCrawler';\nimport DataUpdater from './components/DataUpdater';\nimport PostList from './components/PostList';\nimport { AppProvider } from './context/AppContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AppProvider, {\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex h-screen bg-gray-100\",\n        children: [/*#__PURE__*/_jsxDEV(Sidebar, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 flex flex-col overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n            className: \"flex-1 overflow-x-hidden overflow-y-auto bg-gray-200 p-6\",\n            children: /*#__PURE__*/_jsxDEV(Routes, {\n              children: [/*#__PURE__*/_jsxDEV(Route, {\n                path: \"/\",\n                element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 21,\n                  columnNumber: 42\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 21,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/crawl\",\n                element: /*#__PURE__*/_jsxDEV(DataCrawler, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 22,\n                  columnNumber: 47\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 22,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/update\",\n                element: /*#__PURE__*/_jsxDEV(DataUpdater, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 23,\n                  columnNumber: 48\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 23,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/posts\",\n                element: /*#__PURE__*/_jsxDEV(PostList, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 24,\n                  columnNumber: 47\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 24,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 20,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "<PERSON><PERSON><PERSON>", "Sidebar", "Dashboard", "DataCrawler", "DataUpdater", "PostList", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "App", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Graduation/Frontend/admin/src/App.js"], "sourcesContent": ["import React from 'react';\r\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\r\nimport Navbar from './components/Navbar';\r\nimport Sidebar from './components/Sidebar';\r\nimport Dashboard from './components/Dashboard';\r\nimport DataCrawler from './components/DataCrawler';\r\nimport DataUpdater from './components/DataUpdater';\r\nimport PostList from './components/PostList';\r\nimport { AppProvider } from './context/AppContext';\r\n\r\nfunction App() {\r\n  return (\r\n    <AppProvider>\r\n      <Router>\r\n        <div className=\"flex h-screen bg-gray-100\">\r\n          <Sidebar />\r\n          <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n            <Navbar />\r\n            <main className=\"flex-1 overflow-x-hidden overflow-y-auto bg-gray-200 p-6\">\r\n              <Routes>\r\n                <Route path=\"/\" element={<Dashboard />} />\r\n                <Route path=\"/crawl\" element={<DataCrawler />} />\r\n                <Route path=\"/update\" element={<DataUpdater />} />\r\n                <Route path=\"/posts\" element={<PostList />} />\r\n              </Routes>\r\n            </main>\r\n          </div>\r\n        </div>\r\n      </Router>\r\n    </AppProvider>\r\n  );\r\n}\r\n\r\nexport default App;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,SAASC,WAAW,QAAQ,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACF,WAAW;IAAAI,QAAA,eACVF,OAAA,CAACX,MAAM;MAAAa,QAAA,eACLF,OAAA;QAAKG,SAAS,EAAC,2BAA2B;QAAAD,QAAA,gBACxCF,OAAA,CAACP,OAAO;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACXP,OAAA;UAAKG,SAAS,EAAC,sCAAsC;UAAAD,QAAA,gBACnDF,OAAA,CAACR,MAAM;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACVP,OAAA;YAAMG,SAAS,EAAC,0DAA0D;YAAAD,QAAA,eACxEF,OAAA,CAACV,MAAM;cAAAY,QAAA,gBACLF,OAAA,CAACT,KAAK;gBAACiB,IAAI,EAAC,GAAG;gBAACC,OAAO,eAAET,OAAA,CAACN,SAAS;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC1CP,OAAA,CAACT,KAAK;gBAACiB,IAAI,EAAC,QAAQ;gBAACC,OAAO,eAAET,OAAA,CAACL,WAAW;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjDP,OAAA,CAACT,KAAK;gBAACiB,IAAI,EAAC,SAAS;gBAACC,OAAO,eAAET,OAAA,CAACJ,WAAW;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClDP,OAAA,CAACT,KAAK;gBAACiB,IAAI,EAAC,QAAQ;gBAACC,OAAO,eAAET,OAAA,CAACH,QAAQ;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAElB;AAACG,EAAA,GArBQT,GAAG;AAuBZ,eAAeA,GAAG;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}