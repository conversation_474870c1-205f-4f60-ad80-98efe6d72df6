[{"C:\\Users\\<USER>\\Graduation\\Frontend\\admin\\src\\index.js": "1", "C:\\Users\\<USER>\\Graduation\\Frontend\\admin\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\Graduation\\Frontend\\admin\\src\\App.js": "3", "C:\\Users\\<USER>\\Graduation\\Frontend\\admin\\src\\components\\DataCrawler.jsx": "4", "C:\\Users\\<USER>\\Graduation\\Frontend\\admin\\src\\components\\Navbar.jsx": "5", "C:\\Users\\<USER>\\Graduation\\Frontend\\admin\\src\\components\\Sidebar.jsx": "6", "C:\\Users\\<USER>\\Graduation\\Frontend\\admin\\src\\components\\Dashboard.jsx": "7", "C:\\Users\\<USER>\\Graduation\\Frontend\\admin\\src\\components\\PostList.jsx": "8", "C:\\Users\\<USER>\\Graduation\\Frontend\\admin\\src\\components\\DataUpdater.jsx": "9", "C:\\Users\\<USER>\\Graduation\\Frontend\\admin\\src\\context\\AppContext.jsx": "10", "C:\\Users\\<USER>\\Graduation\\Frontend\\admin\\src\\components\\ProgressBar.jsx": "11", "C:\\Users\\<USER>\\Graduation\\Frontend\\admin\\src\\services\\api.js": "12"}, {"size": 552, "mtime": 1755791680787, "results": "13", "hashOfConfig": "14"}, {"size": 375, "mtime": 1755791680787, "results": "15", "hashOfConfig": "14"}, {"size": 1199, "mtime": 1755791680787, "results": "16", "hashOfConfig": "14"}, {"size": 5641, "mtime": 1755791680787, "results": "17", "hashOfConfig": "14"}, {"size": 529, "mtime": 1755791680787, "results": "18", "hashOfConfig": "14"}, {"size": 1516, "mtime": 1755791680787, "results": "19", "hashOfConfig": "14"}, {"size": 1632, "mtime": 1755791680787, "results": "20", "hashOfConfig": "14"}, {"size": 8239, "mtime": 1755791680787, "results": "21", "hashOfConfig": "14"}, {"size": 12898, "mtime": 1755791680787, "results": "22", "hashOfConfig": "14"}, {"size": 5578, "mtime": 1755791680787, "results": "23", "hashOfConfig": "14"}, {"size": 480, "mtime": 1755791680787, "results": "24", "hashOfConfig": "14"}, {"size": 1362, "mtime": 1755791680787, "results": "25", "hashOfConfig": "14"}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ml0ttm", {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Graduation\\Frontend\\admin\\src\\index.js", [], [], "C:\\Users\\<USER>\\Graduation\\Frontend\\admin\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Graduation\\Frontend\\admin\\src\\App.js", [], [], "C:\\Users\\<USER>\\Graduation\\Frontend\\admin\\src\\components\\DataCrawler.jsx", ["62"], [], "C:\\Users\\<USER>\\Graduation\\Frontend\\admin\\src\\components\\Navbar.jsx", [], [], "C:\\Users\\<USER>\\Graduation\\Frontend\\admin\\src\\components\\Sidebar.jsx", [], [], "C:\\Users\\<USER>\\Graduation\\Frontend\\admin\\src\\components\\Dashboard.jsx", [], [], "C:\\Users\\<USER>\\Graduation\\Frontend\\admin\\src\\components\\PostList.jsx", [], [], "C:\\Users\\<USER>\\Graduation\\Frontend\\admin\\src\\components\\DataUpdater.jsx", ["63"], [], "C:\\Users\\<USER>\\Graduation\\Frontend\\admin\\src\\context\\AppContext.jsx", [], [], "C:\\Users\\<USER>\\Graduation\\Frontend\\admin\\src\\components\\ProgressBar.jsx", [], [], "C:\\Users\\<USER>\\Graduation\\Frontend\\admin\\src\\services\\api.js", [], [], {"ruleId": "64", "severity": 1, "message": "65", "line": 1, "column": 17, "nodeType": "66", "messageId": "67", "endLine": 1, "endColumn": 26}, {"ruleId": "64", "severity": 1, "message": "68", "line": 4, "column": 8, "nodeType": "66", "messageId": "67", "endLine": 4, "endColumn": 19}, "no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'ProgressBar' is defined but never used."]