{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Graduation\\\\Frontend\\\\admin\\\\src\\\\components\\\\Navbar.jsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Navbar = () => {\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"bg-white shadow-md\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center h-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-800\",\n          children: \"Admin Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 8,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-600 mr-4\",\n            children: \"Admin\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 10,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 9,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON>", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Graduation/Frontend/admin/src/components/Navbar.jsx"], "sourcesContent": ["import React from 'react';\r\n\r\nconst Navbar = () => {\r\n  return (\r\n    <header className=\"bg-white shadow-md\">\r\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n        <div className=\"flex justify-between items-center h-16\">\r\n          <h1 className=\"text-2xl font-bold text-gray-800\">Admin Dashboard</h1>\r\n          <div className=\"flex items-center\">\r\n            <span className=\"text-gray-600 mr-4\">Admin</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </header>\r\n  );\r\n};\r\n\r\nexport default Navbar;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,MAAM,GAAGA,CAAA,KAAM;EACnB,oBACED,OAAA;IAAQE,SAAS,EAAC,oBAAoB;IAAAC,QAAA,eACpCH,OAAA;MAAKE,SAAS,EAAC,wCAAwC;MAAAC,QAAA,eACrDH,OAAA;QAAKE,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDH,OAAA;UAAIE,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrEP,OAAA;UAAKE,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChCH,OAAA;YAAME,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACC,EAAA,GAbIP,MAAM;AAeZ,eAAeA,MAAM;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}