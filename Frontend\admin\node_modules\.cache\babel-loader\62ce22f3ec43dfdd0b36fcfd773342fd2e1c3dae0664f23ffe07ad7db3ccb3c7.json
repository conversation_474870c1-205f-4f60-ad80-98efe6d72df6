{"ast": null, "code": "import axios from 'axios';\n\n// Base URLs for different services\nconst CRAWLER_API = 'http://localhost:5000/api/v1';\nconst PREPROCESSOR_API = 'http://localhost:5005/api/v1';\nconst TAGGER_API = 'http://localhost:5010/api/v1';\nconst POSITION_API = 'http://localhost:5015/api/v1';\nconst POST_API = 'http://localhost:8085/api/post';\nexport const startCrawling = cookie => axios.get(`${CRAWLER_API}/scrape`, {\n  params: {\n    cookie\n  }\n});\nexport const stopCrawling = () => axios.post(`${CRAWLER_API}/stop-crawl`);\nexport const updateData = cookie => axios.put(`${CRAWLER_API}/update`, null, {\n  params: {\n    cookie\n  }\n});\nexport const preprocessData = () => axios.post(`${PREPROCESSOR_API}/preprocess`);\nexport const summarizeData = () => axios.post(`${PREPROCESSOR_API}/summarize`);\nexport const tagData = () => axios.post(`${TAGGER_API}/tag-data`);\nexport const tagPosition = () => axios.get(`${POSITION_API}/tag-position`);\nexport const getAllPosts = () => axios.get(`${POST_API}/getAll`);\nexport const getPagedPosts = (limit = 10, page = 1, searchTerm = '') => {\n  try {\n    let url = `${POST_API}/getAll?limit=${limit}&page=${page}`;\n    if (searchTerm) {\n      url += `&search=${encodeURIComponent(searchTerm)}`;\n    }\n    return axios.get(url);\n  } catch (error) {\n    console.log(error);\n  }\n};", "map": {"version": 3, "names": ["axios", "CRAWLER_API", "PREPROCESSOR_API", "TAGGER_API", "POSITION_API", "POST_API", "startCrawling", "cookie", "get", "params", "stopCrawling", "post", "updateData", "put", "preprocessData", "summarizeData", "tagData", "tagPosition", "getAllPosts", "getPagedPosts", "limit", "page", "searchTerm", "url", "encodeURIComponent", "error", "console", "log"], "sources": ["C:/Users/<USER>/Graduation/Frontend/admin/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\r\n\r\n// Base URLs for different services\r\nconst CRAWLER_API = 'http://localhost:5000/api/v1';\r\nconst PREPROCESSOR_API = 'http://localhost:5005/api/v1';\r\nconst TAGGER_API = 'http://localhost:5010/api/v1';\r\nconst POSITION_API = 'http://localhost:5015/api/v1';\r\nconst POST_API = 'http://localhost:8085/api/post';\r\n\r\nexport const startCrawling = (cookie) => axios.get(`${CRAWLER_API}/scrape`, { params: { cookie } });\r\nexport const stopCrawling = () => axios.post(`${CRAWLER_API}/stop-crawl`);\r\nexport const updateData = (cookie) => axios.put(`${CRAWLER_API}/update`, null, { params: { cookie } });\r\n\r\nexport const preprocessData = () => axios.post(`${PREPROCESSOR_API}/preprocess`);\r\nexport const summarizeData = () => axios.post(`${PREPROCESSOR_API}/summarize`);\r\n\r\nexport const tagData = () => axios.post(`${TAGGER_API}/tag-data`);\r\n\r\nexport const tagPosition = () => axios.get(`${POSITION_API}/tag-position`);\r\n\r\nexport const getAllPosts = () => axios.get(`${POST_API}/getAll`);\r\n\r\nexport const getPagedPosts = (limit = 10, page = 1, searchTerm = '') => {\r\n    try {\r\n        let url = `${POST_API}/getAll?limit=${limit}&page=${page}`;\r\n\r\n        if (searchTerm) {\r\n            url += `&search=${encodeURIComponent(searchTerm)}`;\r\n        }\r\n\r\n        return axios.get(url);\r\n    } catch (error) {\r\n        console.log(error);\r\n    }\r\n};"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,WAAW,GAAG,8BAA8B;AAClD,MAAMC,gBAAgB,GAAG,8BAA8B;AACvD,MAAMC,UAAU,GAAG,8BAA8B;AACjD,MAAMC,YAAY,GAAG,8BAA8B;AACnD,MAAMC,QAAQ,GAAG,gCAAgC;AAEjD,OAAO,MAAMC,aAAa,GAAIC,MAAM,IAAKP,KAAK,CAACQ,GAAG,CAAC,GAAGP,WAAW,SAAS,EAAE;EAAEQ,MAAM,EAAE;IAAEF;EAAO;AAAE,CAAC,CAAC;AACnG,OAAO,MAAMG,YAAY,GAAGA,CAAA,KAAMV,KAAK,CAACW,IAAI,CAAC,GAAGV,WAAW,aAAa,CAAC;AACzE,OAAO,MAAMW,UAAU,GAAIL,MAAM,IAAKP,KAAK,CAACa,GAAG,CAAC,GAAGZ,WAAW,SAAS,EAAE,IAAI,EAAE;EAAEQ,MAAM,EAAE;IAAEF;EAAO;AAAE,CAAC,CAAC;AAEtG,OAAO,MAAMO,cAAc,GAAGA,CAAA,KAAMd,KAAK,CAACW,IAAI,CAAC,GAAGT,gBAAgB,aAAa,CAAC;AAChF,OAAO,MAAMa,aAAa,GAAGA,CAAA,KAAMf,KAAK,CAACW,IAAI,CAAC,GAAGT,gBAAgB,YAAY,CAAC;AAE9E,OAAO,MAAMc,OAAO,GAAGA,CAAA,KAAMhB,KAAK,CAACW,IAAI,CAAC,GAAGR,UAAU,WAAW,CAAC;AAEjE,OAAO,MAAMc,WAAW,GAAGA,CAAA,KAAMjB,KAAK,CAACQ,GAAG,CAAC,GAAGJ,YAAY,eAAe,CAAC;AAE1E,OAAO,MAAMc,WAAW,GAAGA,CAAA,KAAMlB,KAAK,CAACQ,GAAG,CAAC,GAAGH,QAAQ,SAAS,CAAC;AAEhE,OAAO,MAAMc,aAAa,GAAGA,CAACC,KAAK,GAAG,EAAE,EAAEC,IAAI,GAAG,CAAC,EAAEC,UAAU,GAAG,EAAE,KAAK;EACpE,IAAI;IACA,IAAIC,GAAG,GAAG,GAAGlB,QAAQ,iBAAiBe,KAAK,SAASC,IAAI,EAAE;IAE1D,IAAIC,UAAU,EAAE;MACZC,GAAG,IAAI,WAAWC,kBAAkB,CAACF,UAAU,CAAC,EAAE;IACtD;IAEA,OAAOtB,KAAK,CAACQ,GAAG,CAACe,GAAG,CAAC;EACzB,CAAC,CAAC,OAAOE,KAAK,EAAE;IACZC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;EACtB;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}