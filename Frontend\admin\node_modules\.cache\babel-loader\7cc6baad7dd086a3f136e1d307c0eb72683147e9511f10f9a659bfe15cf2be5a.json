{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Graduation\\\\Frontend\\\\admin\\\\src\\\\components\\\\Sidebar.jsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Sidebar = () => {\n  _s();\n  const location = useLocation();\n  const isActive = path => {\n    return location.pathname === path ? 'bg-indigo-700 text-white' : 'text-gray-300 hover:bg-indigo-600 hover:text-white';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-indigo-800 w-64 space-y-6 py-7 px-2 absolute inset-y-0 left-0 transform -translate-x-full md:relative md:translate-x-0 transition duration-200 ease-in-out\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-white text-2xl font-semibold\",\n        children: \"Admin Panel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n      children: [/*#__PURE__*/_jsxDEV(Link, {\n        to: \"/\",\n        className: `flex items-center py-2.5 px-4 rounded transition duration-200 ${isActive('/')}`,\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"mx-4\",\n          children: \"Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/crawl\",\n        className: `flex items-center py-2.5 px-4 rounded transition duration-200 ${isActive('/crawl')}`,\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"mx-4\",\n          children: \"Crawl Data\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/update\",\n        className: `flex items-center py-2.5 px-4 rounded transition duration-200 ${isActive('/update')}`,\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"mx-4\",\n          children: \"Update Data\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/posts\",\n        className: `flex items-center py-2.5 px-4 rounded transition duration-200 ${isActive('/posts')}`,\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"mx-4\",\n          children: \"Danh s\\xE1ch b\\xE0i vi\\u1EBFt\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n};\n_s(Sidebar, \"pkHmaVRPskBaU4tMJuJJpV42k1I=\", false, function () {\n  return [useLocation];\n});\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "Link", "useLocation", "jsxDEV", "_jsxDEV", "Sidebar", "_s", "location", "isActive", "path", "pathname", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Graduation/Frontend/admin/src/components/Sidebar.jsx"], "sourcesContent": ["import React from 'react';\r\nimport { Link, useLocation } from 'react-router-dom';\r\n\r\nconst Sidebar = () => {\r\n  const location = useLocation();\r\n  \r\n  const isActive = (path) => {\r\n    return location.pathname === path ? 'bg-indigo-700 text-white' : 'text-gray-300 hover:bg-indigo-600 hover:text-white';\r\n  };\r\n\r\n  return (\r\n    <div className=\"bg-indigo-800 w-64 space-y-6 py-7 px-2 absolute inset-y-0 left-0 transform -translate-x-full md:relative md:translate-x-0 transition duration-200 ease-in-out\">\r\n      <div className=\"flex items-center justify-center\">\r\n        <div className=\"text-white text-2xl font-semibold\">Admin Panel</div>\r\n      </div>\r\n\r\n      <nav>\r\n        <Link to=\"/\" className={`flex items-center py-2.5 px-4 rounded transition duration-200 ${isActive('/')}`}>\r\n          <span className=\"mx-4\">Dashboard</span>\r\n        </Link>\r\n        <Link to=\"/crawl\" className={`flex items-center py-2.5 px-4 rounded transition duration-200 ${isActive('/crawl')}`}>\r\n          <span className=\"mx-4\">Crawl Data</span>\r\n        </Link>\r\n        <Link to=\"/update\" className={`flex items-center py-2.5 px-4 rounded transition duration-200 ${isActive('/update')}`}>\r\n          <span className=\"mx-4\">Update Data</span>\r\n        </Link>\r\n        <Link to=\"/posts\" className={`flex items-center py-2.5 px-4 rounded transition duration-200 ${isActive('/posts')}`}>\r\n          <span className=\"mx-4\">Danh sách bài viết</span>\r\n        </Link>\r\n      </nav>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Sidebar;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAMC,QAAQ,GAAGL,WAAW,CAAC,CAAC;EAE9B,MAAMM,QAAQ,GAAIC,IAAI,IAAK;IACzB,OAAOF,QAAQ,CAACG,QAAQ,KAAKD,IAAI,GAAG,0BAA0B,GAAG,oDAAoD;EACvH,CAAC;EAED,oBACEL,OAAA;IAAKO,SAAS,EAAC,+JAA+J;IAAAC,QAAA,gBAC5KR,OAAA;MAAKO,SAAS,EAAC,kCAAkC;MAAAC,QAAA,eAC/CR,OAAA;QAAKO,SAAS,EAAC,mCAAmC;QAAAC,QAAA,EAAC;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjE,CAAC,eAENZ,OAAA;MAAAQ,QAAA,gBACER,OAAA,CAACH,IAAI;QAACgB,EAAE,EAAC,GAAG;QAACN,SAAS,EAAE,iEAAiEH,QAAQ,CAAC,GAAG,CAAC,EAAG;QAAAI,QAAA,eACvGR,OAAA;UAAMO,SAAS,EAAC,MAAM;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC,eACPZ,OAAA,CAACH,IAAI;QAACgB,EAAE,EAAC,QAAQ;QAACN,SAAS,EAAE,iEAAiEH,QAAQ,CAAC,QAAQ,CAAC,EAAG;QAAAI,QAAA,eACjHR,OAAA;UAAMO,SAAS,EAAC,MAAM;UAAAC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,eACPZ,OAAA,CAACH,IAAI;QAACgB,EAAE,EAAC,SAAS;QAACN,SAAS,EAAE,iEAAiEH,QAAQ,CAAC,SAAS,CAAC,EAAG;QAAAI,QAAA,eACnHR,OAAA;UAAMO,SAAS,EAAC,MAAM;UAAAC,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC,eACPZ,OAAA,CAACH,IAAI;QAACgB,EAAE,EAAC,QAAQ;QAACN,SAAS,EAAE,iEAAiEH,QAAQ,CAAC,QAAQ,CAAC,EAAG;QAAAI,QAAA,eACjHR,OAAA;UAAMO,SAAS,EAAC,MAAM;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACV,EAAA,CA7BID,OAAO;EAAA,QACMH,WAAW;AAAA;AAAAgB,EAAA,GADxBb,OAAO;AA+Bb,eAAeA,OAAO;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}